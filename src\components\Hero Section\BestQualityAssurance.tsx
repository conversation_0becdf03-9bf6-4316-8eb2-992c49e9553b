"use client";
import React from 'react';
import { motion } from 'framer-motion';

const BestQualityAssurance = () => {
  return (
    <div className="flex flex-col items-start">
      {/* Stars Rating */}
      <div className="flex items-center gap-2 mb-2">
        {[...Array(5)].map((_, i) => (
          <motion.span
            key={i}
            className="text-[#F9B87B] text-xl"
            initial={{ scale: 0, rotate: -180 }}
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 10, -10, 0],
              color: ["#F9B87B", "#feb273", "#F9B87B"]
            }}
            transition={{
              duration: 0.5,
              delay: 0.8 + (i * 0.1),
              type: "spring",
              stiffness: 200,
              // Loop animation after entrance
              scale: {
                duration: 2,
                repeat: Infinity,
                delay: 2 + (i * 0.2),
                ease: "easeInOut"
              },
              rotate: {
                duration: 3,
                repeat: Infinity,
                delay: 2.5 + (i * 0.15),
                ease: "easeInOut"
              },
              color: {
                duration: 2.5,
                repeat: Infinity,
                delay: 3 + (i * 0.1),
                ease: "easeInOut"
              }
            }}
          >
            ★
          </motion.span>
        ))}
      </div>

      {/* Best Quality Assurance Text */}
      <motion.div
        className="box-border content-stretch flex flex-col gap-[5px] items-start justify-start leading-[0] p-0 relative shrink-0 text-left text-neutral-900 text-nowrap group"
      >
        <div className="font-['Urbanist:Bold',_sans-serif] font-bold relative shrink-0 text-[47px] tracking-[-0.705px]">
          <motion.p
            className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre text-[#23283B] cursor-pointer"
            whileHover={{ color: ["#23283B", "#feb273", "#23283B"] }}
            transition={{ duration: 2 }}
          >
            Best Quality
          </motion.p>
        </div>
        <div className="font-['Lufga:Regular',_sans-serif] not-italic relative shrink-0 text-[20px] tracking-[-0.3px]">
          <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre text-[#181C2A]">
            Assurance
          </p>
        </div>
      </motion.div>
    </div>
  );
};

export default BestQualityAssurance;
