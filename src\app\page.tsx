import Hero from "../components/Hero Section/Hero";
import { PortfolioGridSection, PortfolioSection } from "../components/sections";

export default function Home() {
  return (
    <main className="relative overflow-x-clip z-[0]">
      {/* ⬜ White Background Wrapper for all below */}
      <div className="relative w-full bg-white z-0">
      <Hero />

      {/* 🟠 Orange Circle Background for Portfolio */}
      <div className="pointer-events-none absolute left-[50%] top-[96vh] -translate-x-1/2 -translate-y-1/2 w-[60rem] h-[60rem] bg-[#F9B87B] rounded-full z-1 opacity-95"></div>

      
        <PortfolioGridSection />
        <PortfolioSection />
      </div>
    </main>
  );
}
