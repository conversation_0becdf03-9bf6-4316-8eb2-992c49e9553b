"use client";

import React from "react";
import { motion } from "framer-motion";
import svgPaths from "../imports/svg-59unyg555t";

function AnimatedSocials() {
  return (
    <motion.div
      className="absolute bg-[#fadcd9] font-['Glegoo:Bold',_sans-serif] h-[153px] leading-[0] left-[644px] not-italic overflow-clip rounded-[20px] text-[#ffffff] text-left top-[110px] w-96"
      data-name="SOCIALS"
      initial={{ scale: 0.9, opacity: 0 }}
      whileInView={{ scale: 1, opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      whileHover={{ scale: 1.02, backgroundColor: "#f5ccc4" }}
      viewport={{ once: true }}
    >
      <motion.div
        className="absolute h-[495px] left-40 text-[40px] top-[-215px] w-[484px]"
        initial={{ y: 20, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        viewport={{ once: true }}
      >
        <p className="block leading-[651px]">PORTFOLIO</p>
      </motion.div>
      <motion.div
        className="absolute h-[377px] left-[9px] text-[64px] top-[-224px] w-[151px]"
        initial={{ x: -20, opacity: 0 }}
        whileInView={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <p className="block leading-[651px]">2024</p>
      </motion.div>
    </motion.div>
  );
}

function AnimatedLine({
  delay = 0,
  width = "516px",
  left = "64px",
  top = "841px",
}) {
  return (
    <motion.div
      className="absolute h-0 w-[516px]"
      style={{ left, top, width }}
      data-name="Animated Line"
      initial={{ scaleX: 0 }}
      whileInView={{ scaleX: 1 }}
      transition={{ duration: 1.5, delay }}
      viewport={{ once: true }}
    >
      <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 516 2"
        >
          <path d="M0 1H516" stroke="var(--stroke-0, black)" />
        </svg>
      </div>
    </motion.div>
  );
}

function AnimatedBlogButton({ left, index, delay = 0 }) {
  return (
    <motion.div
      className="absolute contents"
      style={{ left, top: "1039px" }}
      data-name={`button ${index + 1}`}
      initial={{ y: 30, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, delay }}
      viewport={{ once: true }}
    >
      <motion.div
        className="absolute bg-[#fadcd9] h-[91px] top-[1039px] w-[333px] cursor-pointer"
        style={{ left }}
        data-name="See more"
        whileHover={{
          backgroundColor: "#f0ccc4",
          scale: 1.02,
          y: -2,
        }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
      >
        <div className="absolute border-[#000000] border-[5px] border-solid inset-0 pointer-events-none" />
      </motion.div>

      <motion.div
        className="absolute flex h-[40.992px] items-center justify-center top-[1064px] w-[31.994px]"
        style={{ left: `${parseInt(left) + 267}px` }}
        animate={{ x: [0, 3, 0] }}
        transition={{
          duration: 2,
          repeat: Infinity,
          delay: index * 0.2,
        }}
      >
        <div className="flex-none rotate-[52.028deg]">
          <div className="h-0 relative w-[52.01px]">
            <div className="absolute bottom-[-14.728px] left-0 right-[-3.845%] top-[-14.728px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 54 30"
              >
                <path
                  d={svgPaths.p2a66e780}
                  fill="var(--stroke-0, black)"
                  id="Arrow 6"
                />
              </svg>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[#000000] text-[40px] text-left text-nowrap top-[1061px]"
        style={{ left: `${parseInt(left) + 26}px` }}
        whileHover={{ color: "#666666" }}
        transition={{ duration: 0.2 }}
      >
        <p className="block leading-[normal] whitespace-pre">
          BLOG
        </p>
      </motion.div>
    </motion.div>
  );
}

function AnimatedQuickLinks() {
  const buttonPositions = ["152px", "494px", "836px", "1178px"];

  return (
    <motion.div
      className="absolute contents left-[92px] top-[958px]"
      data-name="Quick Link"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.8 }}
      viewport={{ once: true }}
    >
      {/* Bottom line */}
      <motion.div
        className="absolute h-0 left-[92px] top-[1085px] w-[1535px]"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 2, delay: 1.2 }}
        viewport={{ once: true }}
      >
        <div className="absolute bottom-[-2px] left-0 right-0 top-[-2px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 4"
          >
            <path
              d="M0 2H1535"
              stroke="var(--stroke-0, black)"
              strokeWidth="4"
            />
          </svg>
        </div>
      </motion.div>

      {/* Blog Buttons */}
      {buttonPositions.map((left, index) => (
        <AnimatedBlogButton
          key={index}
          left={left}
          index={index}
          delay={1.0 + index * 0.1}
        />
      ))}

      {/* Quick Links Title */}
      <motion.div
        className="absolute font-['Inter:Black',_sans-serif] font-black leading-[0] left-[152px] not-italic text-[#000000] text-[48px] text-left text-nowrap top-[958px]"
        initial={{ x: -30, opacity: 0 }}
        whileInView={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.9 }}
        viewport={{ once: true }}
      >
        <p className="block leading-[normal] whitespace-pre">
          QUICK LINKS
        </p>
      </motion.div>
    </motion.div>
  );
}

function AnimatedHashtags() {
  const hashtags = [
    {
      text: "#BRANDING   #LOGO",
      left: "244px",
      top: "742.5px",
    },
    {
      text: "#SOCIAL_MEDIA   #POSTER",
      left: "147px",
      top: "823.5px",
    },
    {
      text: "#Illustration   #Packaging",
      left: "153px",
      top: "907.5px",
    },
  ];

  return (
    <div
      className="absolute contents font-['Glegoo:Bold',_sans-serif] leading-[0] left-[147px] not-italic text-[#777777] text-[32px] text-left top-[713px]"
      data-name="Random Text"
    >
      {hashtags.map((hashtag, index) => (
        <motion.div
          key={index}
          className="absolute flex flex-col h-[59px] justify-center translate-y-[-50%] w-[470px]"
          style={{ left: hashtag.left, top: hashtag.top }}
          initial={{ x: -40, opacity: 0 }}
          whileInView={{ x: 0, opacity: 1 }}
          transition={{
            duration: 0.6,
            delay: 0.4 + index * 0.2,
          }}
          viewport={{ once: true }}
        >
          <motion.p
            className="block leading-[615px] whitespace-pre-wrap cursor-pointer"
            whileHover={{ color: "#333333", scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            {hashtag.text}
          </motion.p>
        </motion.div>
      ))}
    </div>
  );
}

export default function AnimatedSection3() {
  return (
    <motion.div
      className="relative size-full"
      data-name="Section 3"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 1 }}
      viewport={{ once: true }}
    >
      {/* Background */}
      <div
        className="absolute bg-[#ffffff] h-[1176px] left-0 top-0 w-[1716px]"
        data-name="Background"
      />

      {/* Main Orange Container */}
      <motion.div
        className="absolute bg-[#feb273] h-[830px] left-[604px] rounded-[68px] top-14 w-[913px]"
        data-name="bg"
        initial={{ scale: 0.9, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.1 }}
        whileHover={{ backgroundColor: "#fd9f4f" }}
        viewport={{ once: true }}
      />

      {/* Socials Badge */}
      <AnimatedSocials />

      {/* Main PORTFOLIO Text */}
      <motion.div
        className="absolute font-['Glegoo:Bold',_sans-serif] leading-[0] left-[846px] not-italic text-[#ffffff] text-[120px] text-left text-nowrap top-[471px]"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, delay: 0.5, type: "spring" }}
        animate={{ x: [0, 5, 0] }}
        transition={{ x: { duration: 4, repeat: Infinity } }}
        viewport={{ once: true }}
      >
        <p className="block leading-[651px] whitespace-pre">
          PORTFOLIO
        </p>
      </motion.div>

      {/* Lorem Ipsum Text */}
      <motion.div
        className="absolute font-['Inter:Regular',_sans-serif] font-normal h-[135px] leading-[0] left-[1490px] not-italic text-[#ffffff] text-[22px] text-right top-[594px] translate-x-[-100%] w-[568px]"
        initial={{ y: 20, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.6 }}
        viewport={{ once: true }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing
          elit.Lorem ipsum dolor sit amet, consectetur
          adipiscing elit. Lorem ipsum dolor sit amet,
          consectetur adipiscing elit.Lorem ipsum dolor sit
          amet, consectetur adipiscing elit.
        </p>
      </motion.div>

      {/* 2025 Text */}
      <motion.div
        className="absolute font-['Glegoo:Bold',_sans-serif] h-[302px] leading-[0] left-[681px] not-italic text-[#ffffff] text-[64px] text-left top-[492px] w-[151px]"
        initial={{ scale: 0, opacity: 0 }}
        whileInView={{ scale: 1, opacity: 1 }}
        transition={{ duration: 1, delay: 0.7, type: "spring" }}
        viewport={{ once: true }}
      >
        <p className="block leading-[651px]">2025</p>
      </motion.div>

      {/* Arrow */}
      <motion.div
        className="absolute flex h-[168.995px] items-center justify-center left-96 top-[387px] w-[122.997px]"
        animate={{
          rotate: [0, 10, -10, 0],
          x: [0, 5, 0],
        }}
        transition={{
          rotate: { duration: 4, repeat: Infinity },
          x: { duration: 3, repeat: Infinity },
        }}
        initial={{ opacity: 0, scale: 0.5 }}
        whileInView={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      >
        <div className="flex-none rotate-[53.952deg]">
          <div className="h-0 relative w-[209.022px]">
            <div className="absolute bottom-[-117.823px] left-[-7.655%] right-[-7.655%] top-[-117.823px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 242 236"
              >
                <path
                  d={svgPaths.p31536a40}
                  fill="var(--stroke-0, black)"
                  id="Arrow 1"
                />
              </svg>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Main horizontal line */}
      <motion.div
        className="absolute h-0 left-[92px] top-[937px] w-[1535px]"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 2, delay: 0.8 }}
        viewport={{ once: true }}
      >
        <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 2"
          >
            <path
              d="M0 1H1535"
              stroke="var(--stroke-0, black)"
            />
          </svg>
        </div>
      </motion.div>

      {/* Decorative Lines */}
      <AnimatedLine delay={0.6} left="64px" top="766px" />
      <AnimatedLine delay={0.8} left="64px" top="841px" />

      {/* Quick Links Section */}
      <AnimatedQuickLinks />

      {/* Hashtags */}
      <AnimatedHashtags />
    </motion.div>
  );
}