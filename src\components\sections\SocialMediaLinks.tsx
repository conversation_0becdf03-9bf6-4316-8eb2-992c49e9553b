"use client";
import React from 'react';
import { motion } from 'framer-motion';

/**
 * SocialMediaLinks Component
 *
 * Displays social media navigation links in a rounded container.
 * Positioned at the top of the orange portfolio container.
 *
 * Design Features:
 * - Pink/beige background (#fadcd9) matching Figma design
 * - Rounded pill shape container
 * - Hover effects with color transitions
 * - Accessibility support with ARIA labels and focus states
 * - Responsive spacing and typography
 */
const SocialMediaLinks = () => {
  // Social media platforms as specified in Figma design
  const socialLinks = [
    { name: 'INSTAGRAM', url: 'https://instagram.com' },
    { name: 'TWITTER', url: 'https://twitter.com' },
    { name: 'LINKEDIN', url: 'https://linkedin.com' }
  ];

  return (
    <div>
      {/*
        Main Container - Pink rounded pill
        Background color: #fadcd9 (brand pink from Figma)
        Shape: Fully rounded (rounded-full)
        Layout: Horizontal flex with centered items
      */}
      <motion.div
        className="bg-[#FADCD9] rounded-[12px] px-8 py-4 flex gap-12 items-center justify-center"
        initial={{ opacity: 0, y: -20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
      >
        {socialLinks.map((link, index) => (
          <motion.a
            key={link.name}
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={`Visit our ${link.name} page`}
            className="text-[#000000] font-['Gilroy:Medium'] text-sm tracking-wider hover:text-[#feb273] transition-colors duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-[#feb273] focus:ring-opacity-50"
            initial={{ opacity: 0, x: -10 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
            whileHover={{ y: -2 }}
            viewport={{ once: true }}
          >
            {link.name}
          </motion.a>
        ))}
      </motion.div>
    </div>
  );
};

export default SocialMediaLinks;
