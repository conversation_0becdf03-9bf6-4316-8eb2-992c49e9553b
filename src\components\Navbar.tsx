"use client";
import React from 'react';
import GetInTouch from './buttons/GetInTouch';

const Navbar = () => {
  return (
    <div className="absolute top-10 left-1/2 -translate-x-1/2 flex items-center w-[60 rem] z-20">
      <nav className="flex items-center justify-center w-[900px] bg-[#0F1117] rounded-full px-14 py-4 shadow-lg">
        <div className="flex gap-14 text-white font-semibold text-xl justify-center w-full">
          <a href="#about" className="hover:text-[#F9B87B] transition-colors">About</a>
          <a href="#services" className="hover:text-[#F9B87B] transition-colors">Services</a>
          <a href="#work" className="hover:text-[#F9B87B] transition-colors">Our Work</a>
          <a href="#blog" className="hover:text-[#F9B87B] transition-colors">Blog</a>
        </div>
      </nav>
      <div className="ml-10 scale-150 py-2">
        <GetInTouch />
      </div>
    </div>
  );
};

export default Navbar;
