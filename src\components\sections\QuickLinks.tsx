'use client';

import { motion } from 'framer-motion';

const ArrowIcon = () => (
  <svg
    className="w-4 h-4 stroke-black group-hover:stroke-white transition-colors"
    viewBox="0 0 24 24"
    fill="none"
    strokeWidth="2"
  >
    <path 
      d="M7 17L17 7M17 7H7M17 7V17" 
      strokeLinecap="round" 
      strokeLinejoin="round"
    />
  </svg>
);

interface BlogButtonProps {
  href: string;
}

const BlogButton = ({ href }: BlogButtonProps) => (
  <motion.a
    href={href}
    className="relative bg-[#FADCD9] border border-black rounded-md px-6 py-4 flex items-center justify-between group hover:bg-black transition-colors"
    whileHover={{ scale: 1.02, y: -2 }}
    whileTap={{ scale: 0.98 }}
  >
    <span className="text-black font-['Gilroy:Bold'] text-lg group-hover:text-white transition-colors">
      BLOG
    </span>
    <ArrowIcon />
  </motion.a>
);

const QuickLinks = () => {
  const blogLinks = Array.from({ length: 4 }, (_, i) => ({
    id: i + 1,
    url: `/blog/${i + 1}`
  }));

  return (
    <div className="w-full">
      <motion.div
        className="w-full"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <motion.h2
          className="text-black font-['Gilroy:Bold'] text-4xl mb-8"
          initial={{ opacity: 0, y: -10 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          QUICK LINKS
        </motion.h2>
        <div className="grid grid-cols-4 gap-4">
          {blogLinks.map((link) => (
            <BlogButton key={link.id} href={link.url} />
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default QuickLinks;
