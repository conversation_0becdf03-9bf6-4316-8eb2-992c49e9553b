"use client";
import React from 'react';
import { motion } from 'framer-motion';

/**
 * PortfolioText Component
 *
 * Main content area displaying the portfolio title and description.
 * Positioned on the right side within the orange container.
 *
 * Design Features:
 * - Right-aligned text layout matching Figma design
 * - Large typography hierarchy (description → year → PORTFOLIO)
 * - White text on orange background for high contrast
 * - Staggered animations for visual appeal
 * - Responsive font sizes for different screen sizes
 */
const PortfolioText = () => {
  return (
    <div>
      <motion.div
        className="max-w-[600px] mx-auto text-center"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true }}
      >
        <motion.p
          className="text-white font-['Gilroy:Light'] text-lg leading-relaxed mb-12 max-w-[480px] mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
        </motion.p>

        <motion.div
          className="flex flex-col items-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="flex items-baseline gap-8">
            <motion.span 
              className="text-white font-['Gilroy:Bold'] text-7xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              2025
            </motion.span>
            <motion.h1
              className="text-white font-['Gilroy:Bold'] text-[120px] tracking-wider leading-none"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              PORTFOLIO
            </motion.h1>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default PortfolioText;
