'use client';
import React from 'react';
import { motion } from 'framer-motion';
import type { CircleDecorationProps } from '../types/portfolio';

const CircleDecoration = ({ size = 300, opacity = 0.2, image, alt }: CircleDecorationProps) => {
  return (
    <motion.div
      className="absolute pointer-events-none"
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity }}
      transition={{ duration: 0.8, ease: 'easeOut' }}
      style={{
        width: size,
        height: size,
        right: 0,
        bottom: 0,
        zIndex: -1
      }}
    >
      <img
        src={image}
        alt={alt}
        className="w-full h-full object-contain"
      />
    </motion.div>
  );
};

export default CircleDecoration;
