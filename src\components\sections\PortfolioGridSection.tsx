"use client";
import React from "react";
import { motion } from "framer-motion";
import CircleDecoration from "../ui/CircleDecoration";
import type { PortfolioGridProps } from "../../types/portfolio";

const PortfolioGridSection: React.FC<PortfolioGridProps> = ({ className = '' }) => {
  
  return (
    <motion.div 
      className={`relative w-full overflow-hidden z-10 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >

      <section className="relative w-full px-6 py-16 z-20">
      {/* Desktop Grid */}
      <div
        className="portfolio-grid w-full max-w-[1400px]  mx-auto z-10 hidden lg:grid relative"
        style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(12, 1fr)',
          gridTemplateRows: 'repeat(6, minmax(140px, 1fr))',
          gridTemplateAreas: [
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"slogan-intro slogan-intro slogan-intro slogan-intro slogan-intro portrait portrait portrait work work work work"',
            '"about about about about contact contact contact contact work work work work"',
            '"about about about about contact contact contact contact social-links social-links social-links social-links"',
            '". . . . . . . . . . . ."'
          ].join(' '),
          gap: '16px',
          minHeight: '800px',
          position: 'relative',
          zIndex: 2
        }}
      >
        {/* Artist Redefining Architecture Card */}
        <motion.div 
          style={{ gridArea: 'slogan-intro' }} 
          className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[56px] leading-[60px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[40px] text-[#000]">AI-Driven Design</div>
          {/* Decorative flower pattern with motion */}
          <motion.div 
            className="absolute top-10 right-5 w-32 h-32"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 0.8 }}
            transition={{ duration: 1, delay: 0.3 }}
          >
            <motion.img 
              src="/img/AI-Driven_Card-Vector.png" 
              alt="Decorative flower" 
              className="w-full h-full object-contain"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            />
          </motion.div>
          {/* Circle Decoration */}
          <CircleDecoration 
            image="/img/AI-Driven_Card-Vector.png"
            alt="Background pattern"
            size={200}
            opacity={0.1}
            className="absolute -bottom-50 -right-20"
          />
        </motion.div>

        {/* Portrait Card */}
        <div style={{ gridArea: 'portrait' }} className="rounded-[20px] min-h-[120px] overflow-hidden">
          <img src="/img/layput_card-2.png" alt="Portrait" className="w-full h-full object-cover" />
        </div>

        {/* Musea Project Card */}
        <div style={{ gridArea: 'work', height: 'calc(100% - 0px)' }} className="bg-[#fadcd9] flex flex-col justify-start items-start p-6 rounded-[20px] min-h-[120px] relative">
          <div className="flex justify-between items-start w-full mb-2">
            <h3 className="font-['Gilroy:Medium',_sans-serif] text-[25px] text-[#000]">Musea</h3>
            <svg width="20" height="20" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
          
          {/* Single large image */}
          <div className="w-full mb-3">
            <img src="/img/layput_card-3.png" alt="Musea Project" className="rounded-lg w-full h-64 object-cover" />
          </div>
          
          {/* Project names in vertical list */}
          <div className="space-y-1 mb-3">
            <div className="font-['Gilroy:Medium',_sans-serif] text-[24px] text-[#000]">Elara</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[24px] text-[#000]">Verve</div>
            <div className="font-['Gilroy:Medium',_sans-serif] text-[24px] text-[#000]">Zephyr</div>
          </div>
          
          {/* Social Frame - Below Musea projects */}
          <motion.div
            className="absolute bottom-6 left-6 right-6"
            initial={{ y: 20, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            viewport={{ once: true }}
          >
            <div className="bg-[rgba(255,255,255,0.3)] backdrop-blur-sm rounded-[12px] p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="bg-[#f8afa6] rounded-full p-2">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L13.09 8.26L19 7L17.74 13.26L24 12L17.74 10.74L19 17L13.09 15.74L12 22L10.91 15.74L5 17L6.26 10.74L0 12L6.26 13.26L5 7L10.91 8.26L12 2Z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-[#000000] font-['Lufga:Medium',_sans-serif] text-[12px]">Social Projects</p>
                    <p className="text-[#666666] font-['Lufga:Light',_sans-serif] text-[10px]">Community driven</p>
                  </div>
                </div>
                <motion.div
                  className="text-[#feb273] cursor-pointer"
                  whileHover={{ x: 3 }}
                >
                  →
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* PER PIXEL About Card */}
        <motion.div
          style={{ gridArea: 'about' }}
          className="bg-[#fadcd9] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative overflow-hidden"
          initial={{ scale: 0.9, opacity: 0 }}
          whileInView={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          whileHover={{ scale: 1.02 }}
          viewport={{ once: true }}
        >
          <motion.img 
            src="/img/PER PIXEL_Layout-Vector.png" 
            alt="Layout Vector"
            className="absolute top-4 left-5 w-12 h-12 opacity-100"
            style={{ zIndex: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
          />
          <div className="font-['Gilroy:Black',_sans-serif] text-[48px] leading-[52px] text-[#000] mb-4 font-black relative z-10">PER PIXEL</div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[20px] text-[#000] leading-relaxed relative z-10">
            Julia Huang is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.
          </div>
        </motion.div>

        {/* Contact Card */}
        <div style={{ gridArea: 'contact' }} className="bg-[#f8afa6] flex flex-col justify-center items-start p-8 rounded-[20px] min-h-[120px] relative">
          <motion.div 
            className="font-['Gilroy:Medium',_sans-serif] text-[55px] text-[#000] mb-2 text-nowrap"
            whileHover={{ color: "#ffffff" }}
            transition={{ duration: 0.3 }}
          >
            Contact me
          </motion.div>
          <div className="font-['Gilroy:Light',_sans-serif] text-[15px] text-[#000] mb-2">Have some questions?</div>
          {/* X icon in top right corner */}
          <div className="absolute top-4 right-4 cursor-pointer hover:opacity-70 transition-opacity">
            <svg width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
        </div>

        {/* Social Links Card */}
        <div style={{ gridArea: 'social-links' }} className="bg-[#fadcd9] flex flex-row justify-between items-center p-4 rounded-[20px] min-h-[60px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[14px] text-[#000] flex gap-8">
            <div className="cursor-pointer hover:opacity-70 transition-opacity">INSTAGRAM</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">TWITTER</div>
            <div className="cursor-pointer hover:opacity-70 transition-opacity">LINKEDIN</div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-[#000] text-sm">Follow us</span>
            <svg width="16" height="16" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24" className="text-[#000]">
              <path d="M7 17L17 7M17 7H7M17 7V17" />
            </svg>
          </div>
        </div>
      </div>

      {/* Tablet Grid - Simplified responsive version */}
      <div className="hidden md:grid lg:hidden grid-cols-6 gap-4 w-full max-w-[900px] mx-auto relative" style={{ zIndex: 2 }}>
        <div className="col-span-3 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] leading-[36px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="col-span-3 bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[150px] object-cover" />
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="col-span-2 bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[32px] text-[#000]">Contact me</div>
        </div>
        <div className="col-span-2 bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[20px] text-[#000]">Musea</div>
        </div>
      </div>

      {/* Mobile Grid - Stack vertically */}
      <div className="grid md:hidden grid-cols-1 gap-4 w-full max-w-[400px] mx-auto relative" style={{ zIndex: 2 }}>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">Artist Redefining</div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] leading-[28px] text-[#000]">
            <span className="italic">Architecture</span> <span className="font-normal">with</span>
          </div>
          <div className="font-['Gilroy:Bold',_sans-serif] text-[20px] leading-[24px] text-[#000]">AI-Driven Design</div>
        </div>
        <div className="bg-white p-6 rounded-[20px] flex justify-center">
          <img src="/img/layput_card-2.png" alt="Portrait" className="rounded-lg max-w-[120px] object-cover" />
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">PER PIXEL</div>
        </div>
        <div className="bg-[#f8afa6] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Bold',_sans-serif] text-[24px] text-[#000]">Contact me</div>
        </div>
        <div className="bg-[#fadcd9] p-6 rounded-[20px]">
          <div className="font-['Gilroy:Medium',_sans-serif] text-[18px] text-[#000]">Musea</div>
        </div>
      </div>
    </section>
      {/* <CircleDecoration 
        image="/img/PER PIXEL_Layout-Vector.png"
        alt="Decorative circle"
        size={300}
        opacity={0.2}
      /> */}
    </motion.div>
  );
};


export default PortfolioGridSection;
