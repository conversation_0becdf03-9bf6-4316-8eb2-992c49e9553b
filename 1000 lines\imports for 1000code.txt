import svgPaths from "./svg-ef293kw09m";
import imgRoundCubeBlackMatte from "figma:asset/212e624bfa920a2f5c150ed2c48f40748de5ec5f.png";
import imgSphereIridescent from "figma:asset/71f8940d5885ea4b99defeb5890ef3cd386126b3.png";
import img from "figma:asset/6d7dce2f8945a9d3d48432859747dd0846373443.png";
import img1 from "figma:asset/b5e59d7a2371454573c4fb23ccaa2c5bd9eb7297.png";
import imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621 from "figma:asset/bdf05f127e983c7a87434379e33db88c745be221.png";
import imgImage from "figma:asset/ab325d5d46b804b1ce04547ea0b4bd6703d39370.png";
import imgRectangle31 from "figma:asset/d18f3ca1c09872c27e4fb88ba1980b61a0d8dbd5.png";
import imgRectangle28 from "figma:asset/b4af99a3c7f40f4c045a5748c8d707912e22f2c1.png";
import imgRectangle26 from "figma:asset/a9014fc1fa453c03c2c52f91fff1f2754e20ad58.png";

function Cube2() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat blur-[2px] filter inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Cube3() {
  return (
    <div className="relative size-full" data-name="Cube-2">
      <div
        className="absolute bg-center bg-cover bg-no-repeat inset-0"
        data-name="RoundCube-Black-Matte"
        style={{ backgroundImage: `url('${imgRoundCubeBlackMatte}')` }}
      />
    </div>
  );
}

function Shapes() {
  return (
    <div
      className="absolute h-[719px] top-6 w-[673px]"
      data-name="Shapes"
      style={{ left: "calc(25% + 45px)" }}
    >
      <div className="absolute bottom-[1.91%] flex items-center justify-center left-[90.359%] right-[-7.412%] top-[82.56%]">
        <div className="flex-none h-[83.202px] rotate-[332.946deg] skew-x-[1.293deg] w-[84.508px]">
          <div
            className="bg-center bg-cover bg-no-repeat size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </div>
      </div>
      <div className="absolute bottom-[81.209%] flex items-center justify-center left-[-74.889%] right-[124.732%] top-[-26.886%]">
        <div className="flex-none h-[244.711px] rotate-[332.946deg] skew-x-[1.293deg] w-[248.554px]">
          <div
            className="bg-center bg-cover bg-no-repeat blur-[2.4px] filter size-full"
            data-name="Sphere-Iridescent"
            style={{ backgroundImage: `url('${imgSphereIridescent}')` }}
          />
        </div>
      </div>
      <div className="absolute flex h-[389.957px] items-center justify-center left-[-74.889%] right-[118.057%] top-[482px]">
        <div className="flex-none h-[313.432px] rotate-[341.68deg] skew-x-[0.465deg] w-[296.572px]">
          <Cube2 />
        </div>
      </div>
      <div className="absolute flex h-[63.31px] items-center justify-center left-[13.133%] right-[77.332%] top-[261.527px]">
        <div className="flex-none h-[50.069px] rotate-[341.68deg] skew-x-[0.465deg] w-[50.609px]">
          <Cube3 />
        </div>
      </div>
      <div className="absolute flex h-[70.701px] items-center justify-center left-[96.021%] right-[-6.669%] top-[411.527px]">
        <div className="flex-none h-[50.351px] rotate-[45.541deg] skew-x-[359.229deg] w-[50.329px]">
          <Cube3 />
        </div>
      </div>
      <div className="absolute flex h-[171.353px] items-center justify-center left-[133.923%] right-[-61.746%] top-[-23.409px]">
        <div className="flex-none h-[166.742px] rotate-[261.528deg] skew-x-[359.453deg] w-[150px]">
          <Cube2 />
        </div>
      </div>
      <div className="absolute flex h-[105.821px] items-center justify-center left-[-15.453%] right-[99.942%] top-[482px]">
        <div className="flex-none h-[77.798px] rotate-[151.763deg] skew-x-[359.229deg] w-[77.764px]">
          <Cube3 />
        </div>
      </div>
    </div>
  );
}

function Links() {
  return (
    <div
      className="box-border content-stretch flex flex-row font-['Montserrat:Bold',_sans-serif] font-bold gap-8 h-[50px] items-center justify-center p-0 relative shrink-0 text-[#ffffff] text-[18px] text-left text-nowrap w-[1284px]"
      data-name="Links"
    >
      <div className="relative shrink-0">
        <p className="block leading-[1.3] text-nowrap whitespace-pre">About</p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[1.3] text-nowrap whitespace-pre">
          Services
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[1.3] text-nowrap whitespace-pre">
          Our Work
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[1.3] text-nowrap whitespace-pre">Blog</p>
      </div>
    </div>
  );
}

function LetsTalk() {
  return (
    <div
      className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0"
      data-name="Lets Talk"
    >
      <div
        className="[background-size:126.44%_111.86%] [grid-area:1_/_1] bg-[50.72%_100%] bg-no-repeat h-[590px] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[197px_28px] mask-size-[128px_46px] ml-[-197px] mt-[-28px] w-[522px]"
        data-name="画形态"
        style={{
          backgroundImage: `url('${img}')`,
          maskImage: `url('${img1}')`,
        }}
      />
      <div
        className="[grid-area:1_/_1] font-['Montserrat:Bold',_sans-serif] font-bold leading-[0] mask-alpha mask-intersect mask-no-clip mask-no-repeat mask-position-[-24.5px_-11px] mask-size-[128px_46px] ml-[24.5px] mt-[11px] relative text-[#ffffff] text-[16px] text-left text-nowrap"
        style={{ maskImage: `url('${img1}')` }}
      >
        <p className="block leading-[1.5] whitespace-pre" dir="auto">
          Let’s Talk
        </p>
      </div>
    </div>
  );
}

function Navbar() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row h-[51.123px] items-center justify-between leading-[0] left-0 px-[124px] py-0 top-[31.035px] w-[1437px]"
      data-name="Navbar"
    >
      <Links />
      <LetsTalk />
    </div>
  );
}

function Navbar1() {
  return (
    <div className="absolute contents left-0 top-[31.035px]" data-name="Navbar">
      <div
        className="absolute bg-[#131d26] h-[44.988px] rounded-[25px] top-[31.104px] w-[836px]"
        style={{ left: "calc(25% - 21px)" }}
      />
      <Navbar />
    </div>
  );
}

function Middle() {
  return (
    <div
      className="absolute contents top-[31px]"
      data-name="Middle"
      style={{ left: "calc(8.333% + 28px)" }}
    >
      <div
        className="absolute h-[405.153px] top-[337.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
      >
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 812 406"
        >
          <path
            d={svgPaths.p3a293080}
            fill="var(--fill-0, #FEB273)"
            id="Ellipse 2"
          />
        </svg>
      </div>
      <div
        className="absolute flex h-[405.153px] items-center justify-center top-[737.282px] w-[811.779px]"
        style={{ left: "calc(25% - 23px)" }}
      >
        <div className="flex-none rotate-[180deg]">
          <div className="h-[405.153px] relative w-[811.779px]">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 812 406"
            >
              <path
                d={svgPaths.p3a293080}
                fill="var(--fill-0, #FEB273)"
                id="Ellipse 3"
              />
            </svg>
          </div>
        </div>
      </div>
      <div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#ffffff] text-[250px] text-left text-nowrap top-[337px]"
        style={{ left: "calc(16.667% + 68px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">Design</p>
      </div>
      <div
        className="absolute font-['Playfair_Display:Bold',_sans-serif] font-bold leading-[0] text-[#131d26] text-[309.341px] text-left text-nowrap top-[31px]"
        style={{ left: "calc(8.333% + 28px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">Creative</p>
      </div>
    </div>
  );
}

function IconOutlinedDirectionsStraightArrowsUpRight() {
  return (
    <div
      className="relative shrink-0 size-[42px]"
      data-name="icon / outlined / directions / straight arrows / up right"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 42 42"
      >
        <g id="icon / outlined / directions / straight arrows / up right">
          <path
            d="M12.25 29.75L29.75 12.25"
            id="Vector"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
          <path
            d="M12.25 12.25H29.75V29.75"
            id="Vector_2"
            stroke="var(--stroke-0, white)"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
          />
        </g>
      </svg>
    </div>
  );
}

function Portfolio() {
  return (
    <div
      className="bg-[#131d26] relative rounded-[60px] shrink-0 w-52"
      data-name="Portfolio"
    >
      <div className="box-border content-stretch flex flex-row items-center justify-center overflow-clip px-5 py-2.5 relative w-52">
        <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
          <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
            Portfolio
          </p>
        </div>
        <IconOutlinedDirectionsStraightArrowsUpRight />
      </div>
      <div className="absolute border-[#d0d5dd] border-[0.5px] border-solid inset-0 pointer-events-none rounded-[60px]" />
    </div>
  );
}

function HireMe() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded-[60px] shrink-0"
      data-name="Hire me"
    >
      <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center px-5 py-2.5 relative w-full">
          <div className="font-['Lufga:Light',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131d26] text-[25.692px] text-left text-nowrap tracking-[-0.3854px]">
            <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
              Hire me
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

function Button5() {
  return (
    <div
      className="absolute backdrop-blur-[7.5px] backdrop-filter bg-[rgba(255,255,255,0.1)] h-[83.841px] rounded-[50px] top-[617.43px] translate-x-[-50%] w-[348px]"
      data-name="Button"
      style={{ left: "calc(54.167% - 44px)" }}
    >
      <div className="box-border content-stretch flex flex-row gap-2.5 h-[83.841px] items-center justify-center overflow-clip p-[10px] relative w-[348px]">
        <Portfolio />
        <HireMe />
      </div>
      <div className="absolute border-2 border-[#ffffff] border-solid inset-0 pointer-events-none rounded-[50px]" />
    </div>
  );
}

function VuesaxBoldQuoteUp() {
  return (
    <div className="absolute contents inset-0" data-name="vuesax/bold/quote-up">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 36 36"
      >
        <g id="quote-up">
          <g id="Vector" opacity="0"></g>
          <path
            d={svgPaths.p35158000}
            fill="var(--fill-0, #344054)"
            id="Vector_2"
          />
          <path
            d={svgPaths.p2b170700}
            fill="var(--fill-0, #344054)"
            id="Vector_3"
          />
        </g>
      </svg>
    </div>
  );
}

function VuesaxBoldQuoteUp1() {
  return (
    <div className="relative shrink-0 size-9" data-name="vuesax/bold/quote-up">
      <VuesaxBoldQuoteUp />
    </div>
  );
}

function Frame4() {
  return (
    <div className="absolute box-border content-stretch flex flex-col gap-6 h-[138.734px] items-start justify-start left-[49px] p-0 top-[371px]">
      <VuesaxBoldQuoteUp1 />
      <div className="font-['Lufga:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#344054] text-[20px] text-left text-nowrap tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
          Jenny’s Exceptional product design
          <br />
          ensure our website’s success.
          <br />
          Highly Recommended
        </p>
      </div>
    </div>
  );
}

function Star() {
  return (
    <div
      className="absolute bottom-[10.411%] left-[8.331%] right-[8.324%] top-[10.417%]"
      data-name="Star"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 27 26"
      >
        <g id="Star">
          <path
            d={svgPaths.p6049d00}
            fill="var(--fill-0, #FD853A)"
            id="Star_2"
          />
        </g>
      </svg>
    </div>
  );
}

function IconStar() {
  return (
    <div className="relative shrink-0 size-8" data-name="icon/Star">
      <Star />
    </div>
  );
}

function Frame5() {
  return (
    <div className="box-border content-stretch flex flex-row items-start justify-center p-0 relative shrink-0">
      {[...Array(5).keys()].map((_, i) => (
        <IconStar key={i} />
      ))}
    </div>
  );
}

function Frame3() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[5px] items-end justify-start leading-[0] p-0 relative shrink-0 text-center text-neutral-900 text-nowrap">
      <div className="font-['Urbanist:Bold',_sans-serif] font-bold relative shrink-0 text-[47px] tracking-[-0.705px]">
        <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre">
          10 Years
        </p>
      </div>
      <div className="font-['Lufga:Regular',_sans-serif] not-italic relative shrink-0 text-[20px] tracking-[-0.3px]">
        <p className="adjustLetterSpacing block leading-none text-nowrap whitespace-pre">
          Experince
        </p>
      </div>
    </div>
  );
}

function Frame6() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-col gap-[21px] h-[125.665px] items-end justify-start p-0 top-[605px]"
      style={{ left: "calc(83.333% + 51px)" }}
    >
      <Frame5 />
      <Frame3 />
    </div>
  );
}

function HeroSection() {
  return (
    <div className="absolute contents left-0 top-6" data-name="Hero Section">
      <Shapes />
      <Navbar1 />
      <Middle />
      <Button5 />
      <Frame4 />
      <Frame6 />
    </div>
  );
}

function Links1() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row font-['Gilroy:Light',_sans-serif] items-center justify-between leading-[0] left-1/2 not-italic p-0 text-[#000000] text-[15px] text-left text-nowrap top-1/2 translate-x-[-50%] translate-y-[-50%] uppercase w-[334px]"
      data-name="LINKS"
    >
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Instagram
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Twitter
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Linkedin
        </p>
      </div>
    </div>
  );
}

function Socials() {
  return (
    <div
      className="absolute bg-[#fadcd9] h-[101px] overflow-clip rounded-[20px] top-[1513px] w-[448px]"
      data-name="SOCIALS"
      style={{ left: "calc(66.667% + 6px)" }}
    >
      <Links1 />
    </div>
  );
}

function Arrow() {
  return (
    <div className="relative shrink-0 size-[38px]" data-name="ARROW">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 38 38"
      >
        <g id="ARROW">
          <path
            clipRule="evenodd"
            d={svgPaths.p17200c00}
            fill="var(--fill-0, black)"
            fillRule="evenodd"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function TopBar() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row items-end justify-between left-1/2 p-0 top-[30px] translate-x-[-50%] w-[400px]"
      data-name="TOP BAR"
    >
      <div className="font-['Gilroy:Light',_sans-serif] leading-[normal] not-italic relative shrink-0 text-[#000000] text-[15px] text-left text-nowrap whitespace-pre">
        <p className="block mb-0">Have some</p>
        <p className="block">questions?</p>
      </div>
      <Arrow />
    </div>
  );
}

function Contact() {
  return (
    <div
      className="absolute bg-[#f8afa6] h-[351px] overflow-clip rounded-[20px] top-[1263px] w-[448px]"
      data-name="CONTACT"
      style={{ left: "calc(33.333% + 14px)" }}
    >
      <div className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[55px] text-left text-nowrap top-[259px]">
        <p className="block leading-[normal] whitespace-pre">Contact me</p>
      </div>
      <TopBar />
    </div>
  );
}

function CircleIcon() {
  return (
    <div className="absolute left-6 size-[38px] top-8" data-name="CIRCLE ICON">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 38 38"
      >
        <g clipPath="url(#clip0_1_396)" id="CIRCLE ICON">
          <path
            d={svgPaths.p21c04900}
            fill="var(--fill-0, #F8AFA6)"
            id="Vector"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_396">
            <rect fill="white" height="38" width="38" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

function About() {
  return (
    <div
      className="absolute bg-[#fadcd9] h-[351px] left-[22px] overflow-clip rounded-[20px] top-[1263px] w-[448px]"
      data-name="ABOUT"
    >
      <div
        className="absolute font-['Gilroy:Light',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[20px] text-left w-[296px]"
        style={{ top: "calc(50% - 20.5px)" }}
      >
        <p className="block leading-[25px]">
          Julia Huang is an innovative AI artist, renowned for blending
          cutting-edge technology with creative expression. Based in LA, she
          crafts unique digital art experiences accessible globally.
        </p>
      </div>
      <CircleIcon />
    </div>
  );
}

function Item4() {
  return (
    <div className="absolute contents left-6 top-[651px]" data-name="ITEM 4">
      <div className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[651px]">
        <p className="block leading-[normal] whitespace-pre">Zephyr</p>
      </div>
    </div>
  );
}

function Item3() {
  return (
    <div className="absolute contents left-6 top-[538px]" data-name="ITEM 3">
      <div
        className="absolute h-0 left-1/2 top-[609px] translate-x-[-50%] w-[399px]"
        data-name="BORDER"
      >
        <div
          className="absolute bottom-0 left-0 right-0 top-[-2px]"
          style={
            { "--stroke-0": "rgba(248, 175, 166, 1)" } as React.CSSProperties
          }
        >
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 399 2"
          >
            <line
              id="BORDER"
              stroke="var(--stroke-0, #F8AFA6)"
              strokeWidth="2"
              x2="399"
              y1="1"
              y2="1"
            />
          </svg>
        </div>
      </div>
      <div className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[538px]">
        <p className="block leading-[normal] whitespace-pre">Verve</p>
      </div>
    </div>
  );
}

function Item2() {
  return (
    <div className="absolute contents left-6 top-[425px]" data-name="ITEM 2">
      <div
        className="absolute h-0 left-1/2 top-[497px] translate-x-[-50%] w-[399px]"
        data-name="BORDER"
      >
        <div
          className="absolute bottom-0 left-0 right-0 top-[-2px]"
          style={
            { "--stroke-0": "rgba(248, 175, 166, 1)" } as React.CSSProperties
          }
        >
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 399 2"
          >
            <line
              id="BORDER"
              stroke="var(--stroke-0, #F8AFA6)"
              strokeWidth="2"
              x2="399"
              y1="1"
              y2="1"
            />
          </svg>
        </div>
      </div>
      <div className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[425px]">
        <p className="block leading-[normal] whitespace-pre">Elara</p>
      </div>
    </div>
  );
}

function Image() {
  return (
    <div
      className="absolute h-[269px] left-6 overflow-clip rounded-2xl top-[85px] w-[399px]"
      data-name="IMAGE"
    >
      <div
        className="absolute bg-center bg-cover bg-no-repeat h-[400px] left-0 top-[-113px] w-[399px]"
        data-name="lilcoderman_pink_chair_sitting_on_table_in_a_room_in_the_style__8f5e5aa9-38f8-4af0-89f3-8572b0ae9362 1"
        style={{
          backgroundImage: `url('${imgLilcodermanPinkChairSittingOnTableInARoomInTheStyle8F5E5Aa938F84Af089F38572B0Ae93621}')`,
        }}
      />
    </div>
  );
}

function ArrowIcon() {
  return (
    <div
      className="absolute left-[397px] size-[26px] top-9"
      data-name="ARROW ICON"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 26 26"
      >
        <g id="ARROW ICON">
          <path
            clipRule="evenodd"
            d={svgPaths.pd563480}
            fill="var(--fill-0, black)"
            fillRule="evenodd"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Item1() {
  return (
    <div className="absolute contents left-6 top-[34px]" data-name="ITEM 1">
      <div
        className="absolute h-0 left-1/2 top-96 translate-x-[-50%] w-[399px]"
        data-name="BORDER"
      >
        <div
          className="absolute bottom-0 left-0 right-0 top-[-2px]"
          style={
            { "--stroke-0": "rgba(248, 175, 166, 1)" } as React.CSSProperties
          }
        >
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 399 2"
          >
            <line
              id="BORDER"
              stroke="var(--stroke-0, #F8AFA6)"
              strokeWidth="2"
              x2="399"
              y1="1"
              y2="1"
            />
          </svg>
        </div>
      </div>
      <Image />
      <ArrowIcon />
      <div className="absolute font-['Gilroy:Medium',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[25px] text-left text-nowrap top-[34px]">
        <p className="block leading-[normal] whitespace-pre">Musea</p>
      </div>
    </div>
  );
}

function Work() {
  return (
    <div
      className="absolute bg-[#fadcd9] h-[726px] overflow-clip rounded-[20px] top-[763px] w-[447px]"
      data-name="WORK"
      style={{ left: "calc(66.667% + 6px)" }}
    >
      <Item4 />
      <Item3 />
      <Item2 />
      <Item1 />
    </div>
  );
}

function Portrait() {
  return (
    <div
      className="absolute h-[476px] overflow-clip rounded-[20px] top-[763px] w-[330px]"
      data-name="PORTRAIT"
      style={{ left: "calc(41.667% + 12px)" }}
    >
      <div
        className="absolute bg-center bg-cover bg-no-repeat h-[556px] left-[-25px] top-[-19px] w-[355px]"
        data-name="IMAGE"
        style={{ backgroundImage: `url('${imgImage}')` }}
      />
    </div>
  );
}

function FlowerIcon() {
  return (
    <div
      className="absolute left-[416px] size-[119px] top-[34px]"
      data-name="FLOWER ICON"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 119 119"
      >
        <g clipPath="url(#clip0_1_366)" id="FLOWER ICON">
          <path
            d={svgPaths.p12a08980}
            fill="var(--fill-0, #F8AFA6)"
            id="Vector"
          />
        </g>
        <defs>
          <clipPath id="clip0_1_366">
            <rect fill="white" height="119" width="119" />
          </clipPath>
        </defs>
      </svg>
    </div>
  );
}

function SloganIntro() {
  return (
    <div
      className="absolute bg-[#fadcd9] h-[479px] left-[22px] overflow-clip rounded-[20px] top-[760px] w-[565px]"
      data-name="SLOGAN/INTRO"
    >
      <div
        className="absolute font-['Gilroy:Bold',_sans-serif] leading-[0] left-6 not-italic text-[#000000] text-[0px] text-left w-[475px]"
        style={{ top: "calc(50% - 14.5px)" }}
      >
        <p className="leading-none text-[56px]">
          <span>{`Artist Redefining `}</span>
          <span className="font-['Gilroy:Light_Italic',_sans-serif] not-italic">
            Architecture
          </span>
          <span>{` with AI-Driven Design`}</span>
        </p>
      </div>
      <FlowerIcon />
    </div>
  );
}

function Section2() {
  return (
    <div
      className="absolute contents left-[22px] top-[760px]"
      data-name="Section 2"
    >
      <Socials />
      <Contact />
      <About />
      <Work />
      <Portrait />
      <SloganIntro />
      <div className="absolute font-['Inter:Black',_sans-serif] font-black h-[77px] leading-[0] left-[46px] not-italic text-[#131d26] text-[78px] text-left top-[1316px] tracking-[1.56px] w-[430px]">
        <p className="adjustLetterSpacing block leading-[651px]">PER PIXEL</p>
      </div>
    </div>
  );
}

function Links2() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row font-['Gilroy:Light',_sans-serif] items-center justify-between leading-[0] left-1/2 not-italic p-0 text-[#000000] text-[15px] text-left text-nowrap top-1/2 translate-x-[-50%] translate-y-[-50%] uppercase w-[334px]"
      data-name="LINKS"
    >
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Instagram
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Twitter
        </p>
      </div>
      <div className="relative shrink-0">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Linkedin
        </p>
      </div>
    </div>
  );
}

function Socials1() {
  return (
    <div
      className="absolute bg-[#fadcd9] h-[101px] overflow-clip rounded-[20px] top-[1737px] w-[448px]"
      data-name="SOCIALS"
      style={{ left: "calc(33.333% + 61px)" }}
    >
      <Links2 />
    </div>
  );
}

function Text2() {
  return (
    <div
      className="absolute h-0 left-[-40px] top-[2443px] w-[516px]"
      data-name="Text 2"
    >
      <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 516 2"
        >
          <g id="Text 2">
            <path d="M0 1H516" id="Arrow 4" stroke="var(--stroke-0, black)" />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Text1() {
  return (
    <div
      className="absolute h-0 left-[-40px] top-[2368px] w-[516px]"
      data-name="Text 1"
    >
      <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 516 2"
        >
          <g id="Text 2">
            <path d="M0 1H516" id="Arrow 4" stroke="var(--stroke-0, black)" />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Button1() {
  return (
    <div
      className="absolute contents left-12 top-[2641px]"
      data-name="button 1"
    >
      <div
        className="absolute bg-[#fadcd9] h-[91px] left-12 top-[2641px] w-[333px]"
        data-name="See more"
      >
        <div className="absolute border-[#000000] border-[5px] border-solid inset-0 pointer-events-none" />
      </div>
      <div
        className="absolute flex h-[40.992px] items-center justify-center top-[2666px] w-[31.994px]"
        style={{ left: "calc(16.667% + 75px)" }}
      >
        <div className="flex-none rotate-[52.028deg]">
          <div className="h-0 relative w-[52.01px]">
            <div className="absolute bottom-[-14.728px] left-0 right-[-3.845%] top-[-14.728px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 54 30"
              >
                <path
                  d={svgPaths.p2a66e780}
                  fill="var(--stroke-0, black)"
                  id="Arrow 6"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] left-[74px] not-italic text-[#000000] text-[40px] text-left text-nowrap top-[2663px]">
        <p className="block leading-[normal] whitespace-pre">BLOG</p>
      </div>
    </div>
  );
}

function Button2() {
  return (
    <div
      className="absolute contents top-[2641px]"
      data-name="button 2"
      style={{ left: "calc(25% + 30px)" }}
    >
      <div
        className="absolute bg-[#fadcd9] h-[91px] top-[2641px] w-[333px]"
        data-name="See more"
        style={{ left: "calc(25% + 30px)" }}
      >
        <div className="absolute border-[#000000] border-[5px] border-solid inset-0 pointer-events-none" />
      </div>
      <div
        className="absolute flex h-[40.992px] items-center justify-center top-[2666px] w-[31.994px]"
        style={{ left: "calc(41.667% + 57px)" }}
      >
        <div className="flex-none rotate-[52.028deg]">
          <div className="h-0 relative w-[52.01px]">
            <div className="absolute bottom-[-14.728px] left-0 right-[-3.845%] top-[-14.728px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 54 30"
              >
                <path
                  d={svgPaths.p2a66e780}
                  fill="var(--stroke-0, black)"
                  id="Arrow 6"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[#000000] text-[40px] text-left text-nowrap top-[2663px]"
        style={{ left: "calc(25% + 56px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">BLOG</p>
      </div>
    </div>
  );
}

function Button3() {
  return (
    <div
      className="absolute contents top-[2641px]"
      data-name="button 3"
      style={{ left: "calc(50% + 12px)" }}
    >
      <div
        className="absolute bg-[#fadcd9] h-[91px] top-[2641px] w-[333px]"
        data-name="See more"
        style={{ left: "calc(50% + 12px)" }}
      >
        <div className="absolute border-[#000000] border-[5px] border-solid inset-0 pointer-events-none" />
      </div>
      <div
        className="absolute flex h-[40.992px] items-center justify-center top-[2666px] w-[31.994px]"
        style={{ left: "calc(66.667% + 39px)" }}
      >
        <div className="flex-none rotate-[52.028deg]">
          <div className="h-0 relative w-[52.01px]">
            <div className="absolute bottom-[-14.728px] left-0 right-[-3.845%] top-[-14.728px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 54 30"
              >
                <path
                  d={svgPaths.p2a66e780}
                  fill="var(--stroke-0, black)"
                  id="Arrow 6"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[#000000] text-[40px] text-left text-nowrap top-[2663px]"
        style={{ left: "calc(50% + 38px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">BLOG</p>
      </div>
    </div>
  );
}

function Button4() {
  return (
    <div
      className="absolute contents top-[2641px]"
      data-name="button 4"
      style={{ left: "calc(75% - 6px)" }}
    >
      <div
        className="absolute bg-[#fadcd9] h-[91px] top-[2641px] w-[333px]"
        data-name="See more"
        style={{ left: "calc(75% - 6px)" }}
      >
        <div className="absolute border-[#000000] border-[5px] border-solid inset-0 pointer-events-none" />
      </div>
      <div
        className="absolute flex h-[40.992px] items-center justify-center top-[2666px] w-[31.994px]"
        style={{ left: "calc(91.667% + 21px)" }}
      >
        <div className="flex-none rotate-[52.028deg]">
          <div className="h-0 relative w-[52.01px]">
            <div className="absolute bottom-[-14.728px] left-0 right-[-3.845%] top-[-14.728px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 54 30"
              >
                <path
                  d={svgPaths.p2a66e780}
                  fill="var(--stroke-0, black)"
                  id="Arrow 6"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div
        className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[#000000] text-[40px] text-left text-nowrap top-[2663px]"
        style={{ left: "calc(75% + 20px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">BLOG</p>
      </div>
    </div>
  );
}

function QuickLink() {
  return (
    <div
      className="absolute contents left-[-12px] top-[2560px]"
      data-name="Quick Link"
    >
      <div className="absolute h-0 left-[-12px] top-[2687px] w-[1535px]">
        <div className="absolute bottom-[-2px] left-0 right-0 top-[-2px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 4"
          >
            <path
              d="M0 2H1535"
              id="Arrow 4"
              stroke="var(--stroke-0, black)"
              strokeWidth="4"
            />
          </svg>
        </div>
      </div>
      <Button1 />
      <Button2 />
      <Button3 />
      <Button4 />
      <div className="absolute font-['Inter:Black',_sans-serif] font-black leading-[0] left-12 not-italic text-[#000000] text-[48px] text-left text-nowrap top-[2560px]">
        <p className="block leading-[normal] whitespace-pre">QUICK LINKS</p>
      </div>
    </div>
  );
}

function Section3() {
  return (
    <div
      className="absolute contents left-[-40px] top-[1658px]"
      data-name="Section 3"
    >
      <div
        className="absolute bg-[#feb273] h-[830px] rounded-[68px] top-[1658px] w-[913px]"
        data-name="bg"
        style={{ left: "calc(33.333% + 20px)" }}
      />
      <Socials1 />
      <div
        className="absolute font-['Glegoo:Bold',_sans-serif] leading-[0] not-italic text-[#ffffff] text-[120px] text-left text-nowrap top-[2073px]"
        style={{ left: "calc(50% + 22px)" }}
      >
        <p className="block leading-[651px] whitespace-pre">PORTFOLIO</p>
      </div>
      <div
        className="absolute font-['Inter:Regular',_sans-serif] font-normal h-[135px] leading-[0] not-italic text-[#ffffff] text-[22px] text-right top-[2196px] translate-x-[-100%] w-[568px]"
        style={{ left: "calc(50% + 666px)" }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
          amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet,
          consectetur adipiscing elit.
        </p>
      </div>
      <div
        className="absolute font-['Glegoo:Bold',_sans-serif] h-[302px] leading-[0] not-italic text-[#ffffff] text-[64px] text-left top-[2094px] w-[151px]"
        style={{ left: "calc(33.333% + 97px)" }}
      >
        <p className="block leading-[651px]">2025</p>
      </div>
      <div
        className="absolute flex h-[168.995px] items-center justify-center top-[1989px] w-[122.997px]"
        style={{ left: "calc(16.667% + 40px)" }}
      >
        <div className="flex-none rotate-[53.952deg]">
          <div className="h-0 relative w-[209.022px]">
            <div className="absolute bottom-[-117.823px] left-[-7.655%] right-[-7.655%] top-[-117.823px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 242 236"
              >
                <path
                  d={svgPaths.p31536a40}
                  fill="var(--stroke-0, black)"
                  id="Arrow 1"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute h-0 left-[-12px] top-[2539px] w-[1535px]">
        <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 2"
          >
            <path d="M0 1H1535" id="Arrow 3" stroke="var(--stroke-0, black)" />
          </svg>
        </div>
      </div>
      <Text2 />
      <Text1 />
      <QuickLink />
    </div>
  );
}

function NameAndInfo() {
  return (
    <div
      className="absolute contents left-12 top-[2915px]"
      data-name="Name  and Info"
    >
      <div className="absolute bg-[#fadcd9] h-[670px] left-12 rounded-[54px] top-[2915px] w-[440px]" />
    </div>
  );
}

function Introduction() {
  return (
    <div
      className="absolute h-[272px] top-[2998px] w-[1535px]"
      data-name="Introduction"
      style={{ left: "calc(33.333% + 32px)" }}
    >
      <div className="absolute bottom-[-0.184%] left-0 right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 1535 273"
        >
          <g id="Introduction">
            <rect
              fill="var(--fill-0, #FADCD9)"
              height="231"
              id="Rectangle 33"
              rx="54"
              width="852"
            />
            <path
              d="M0 272H1535"
              id="Arrow 5"
              stroke="var(--stroke-0, black)"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Button6() {
  return (
    <div
      className="absolute contents top-[3390px]"
      data-name="Button"
      style={{ left: "calc(33.333% + 32px)" }}
    >
      <div
        className="absolute bg-[#d9d9d9] h-[195px] rounded-[54px] top-[3390px] w-[277.269px]"
        style={{ left: "calc(33.333% + 32px)" }}
      />
      <div
        className="absolute bg-[#d9d9d9] h-[195px] rounded-[54px] top-[3390px] w-[277.269px]"
        style={{ left: "calc(50% + 90.365px)" }}
      />
      <div
        className="absolute bg-[#d9d9d9] h-[195px] rounded-[54px] top-[3390px] w-[277.269px]"
        style={{ left: "calc(75% + 28.731px)" }}
      />
    </div>
  );
}

function Section4() {
  return (
    <div
      className="absolute contents left-[-12px] top-[2796px]"
      data-name="Section 4"
    >
      <div className="absolute h-0 left-[-12px] top-[2796px] w-[1535px]">
        <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 2"
          >
            <path d="M0 1H1535" id="Arrow 3" stroke="var(--stroke-0, black)" />
          </svg>
        </div>
      </div>
      <div className="absolute h-0 left-[-12px] top-[2866px] w-[1535px]">
        <div className="absolute bottom-[-0.5px] left-0 right-0 top-[-0.5px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1535 2"
          >
            <path d="M0 1H1535" id="Arrow 3" stroke="var(--stroke-0, black)" />
          </svg>
        </div>
      </div>
      <NameAndInfo />
      <Introduction />
      <Button6 />
      <div
        className="absolute h-[70px] top-[3299px] w-[74px]"
        style={{ left: "calc(83.333% + 112px)" }}
      >
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 74 70"
        >
          <ellipse
            cx="37"
            cy="35"
            fill="var(--fill-0, #FEB273)"
            id="Ellipse 4"
            rx="37"
            ry="35"
          />
        </svg>
      </div>
      <div
        className="absolute font-['Inter:Black',_sans-serif] font-black h-[68px] leading-[0] not-italic text-[#000000] text-[48px] text-left top-[3299px] w-[478px]"
        style={{ left: "calc(33.333% + 46px)" }}
      >
        <p className="block leading-[normal]">Get in Touch</p>
      </div>
      <div
        className="absolute font-['Inter:Black',_sans-serif] font-black h-[70px] leading-[0] not-italic text-[#000000] text-[48px] text-left top-[2915px] w-[780px]"
        style={{ left: "calc(33.333% + 46px)" }}
      >
        <p className="block leading-[normal]">Introduction</p>
      </div>
      <div className="absolute h-0 left-[28.965px] top-[2853.5px] w-[179.07px]">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 180 1"
          >
            <line
              id="Line 1"
              stroke="var(--stroke-0, black)"
              x2="179.07"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <div className="absolute font-['Inter:Light',_sans-serif] font-light h-[43px] leading-[0] left-[116.5px] not-italic text-[#000000] text-[32px] text-center top-[2811px] translate-x-[-50%] w-[195px]">
        <p className="block leading-[normal]">About Us</p>
      </div>
    </div>
  );
}

function Title() {
  return (
    <div
      className="absolute contents top-[3640px]"
      data-name="Title"
      style={{ left: "calc(8.333% + 42px)" }}
    >
      <div
        className="absolute font-['Playfair_Display:Regular',_sans-serif] font-normal leading-[0] text-[#000000] text-[81.308px] text-left text-nowrap top-[3640px]"
        style={{ left: "calc(33.333% + 8.371px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">Testimonials</p>
      </div>
      <div
        className="absolute font-['Inter:Regular',_sans-serif] font-normal leading-[0] not-italic text-[#000000] text-[22px] text-center top-[3773.12px] translate-x-[-50%] w-[1119.74px]"
        style={{ left: "calc(8.333% + 601.871px)" }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
          amet, consectetur adipiscing elit.Lorem ipsum dolor sit amet,
          consectetur adipiscing elit.
        </p>
      </div>
      <div
        className="absolute h-0 top-[3802px] w-[100px]"
        style={{ left: "calc(50% + 49px)" }}
      >
        <div className="absolute bottom-[-7.364px] left-0 right-[-1%] top-[-7.364px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 101 16"
          >
            <path
              d={svgPaths.p2744e400}
              fill="var(--stroke-0, black)"
              id="Arrow 2"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Review1() {
  return (
    <div
      className="absolute contents top-[3883.41px]"
      data-name="Review 1"
      style={{ left: "calc(8.333% + 30px)" }}
    >
      <div
        className="absolute flex h-[309px] items-center justify-center top-[3883.41px] w-[327.063px]"
        style={{ left: "calc(8.333% + 30px)" }}
      >
        <div className="flex-none rotate-[270deg]">
          <div
            className="[background-size:6.39325e+09%_1.25769e+10%] bg-[-0.27%_97%] bg-no-repeat h-[327.077px] w-[309.005px]"
            style={{ backgroundImage: `url('${imgRectangle31}')` }}
          />
        </div>
      </div>
      <div
        className="absolute bg-[#f9cec9] h-[309.005px] top-[3883.41px] w-[430.408px]"
        style={{ left: "calc(33.333% - 2.923px)" }}
      />
      <div
        className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#000000] text-[44px] text-left top-[3904.63px] w-[417.167px]"
        style={{ left: "calc(33.333% + 20.399px)" }}
      >
        <p className="block leading-[1.015]">“Amazing Team with Lorem Ipsum”</p>
      </div>
      <div
        className="absolute font-['Inter:Light',_sans-serif] font-light leading-[0] not-italic text-[#000000] text-[15px] text-left top-[4023.92px] w-[290.207px]"
        style={{ left: "calc(33.333% + 20.399px)" }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
          amet,
        </p>
      </div>
      <div
        className="absolute font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic text-[#000000] text-[16.067px] text-nowrap text-right top-[4144.95px] translate-x-[-100%]"
        style={{ left: "calc(33.333% + 134.566px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">{`See full review `}</p>
      </div>
      <div
        className="absolute h-[19.431px] top-[4147.23px] w-[30.276px]"
        style={{ left: "calc(41.667% + 22.141px)" }}
      >
        <div className="absolute bottom-[-3.984%] left-0 right-[-5.114%] top-[-3.984%]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 32 22"
          >
            <path
              d={svgPaths.ped78e80}
              id="Vector 6"
              stroke="var(--stroke-0, #060606)"
              strokeWidth="2.18959"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Review2() {
  return (
    <div
      className="absolute contents top-[3883.41px]"
      data-name="Review 2"
      style={{ left: "calc(66.667% + 2.103px)" }}
    >
      <div
        className="absolute flex h-[309px] items-center justify-center top-[3883.41px] w-[327.063px]"
        style={{ left: "calc(66.667% + 2.103px)" }}
      >
        <div className="flex-none rotate-[270deg]">
          <div
            className="[background-size:6.40285e+09%_1.81791e+10%] bg-[0.15%_80.39%] bg-no-repeat h-[327.077px] w-[309.005px]"
            style={{ backgroundImage: `url('${imgRectangle28}')` }}
          />
        </div>
      </div>
      <div
        className="absolute bg-[#181818] h-[362.119px] top-[4192.42px] w-[327.077px]"
        style={{ left: "calc(66.667% + 2.103px)" }}
      />
      <div
        className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#ffffff] text-[44px] text-left top-[4213.64px] w-[279.368px]"
        style={{ left: "calc(66.667% + 19.826px)" }}
      >
        <p className="block leading-[1.015]">“Good Dreams for lorem ipsum”</p>
      </div>
      <div
        className="absolute font-['Inter:Light',_sans-serif] font-light leading-[0] not-italic text-[#ffffff] text-[15px] text-left top-[4362.92px] w-[220.535px]"
        style={{ left: "calc(66.667% + 19.826px)" }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
          amet,
        </p>
      </div>
      <div
        className="absolute font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic text-[#ffffff] text-[16.067px] text-right top-[4501.37px] translate-x-[-100%] w-[122.733px]"
        style={{ left: "calc(66.667% + 131.827px)" }}
      >
        <p className="block leading-[normal]">{`See full review `}</p>
      </div>
      <div
        className="absolute h-[19.431px] top-[4501.37px] w-[23.007px]"
        style={{ left: "calc(75% + 22.634px)" }}
      >
        <div className="absolute bottom-[-3.409%] left-0 right-[-5.977%] top-[-3.409%]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 25 22"
          >
            <path
              d={svgPaths.p207700}
              id="Vector 8"
              stroke="var(--stroke-0, white)"
              strokeWidth="2.18959"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Review3() {
  return (
    <div
      className="absolute contents top-[4245.53px]"
      data-name="Review 3"
      style={{ left: "calc(8.333% + 30px)" }}
    >
      <div
        className="absolute flex h-[309px] items-center justify-center top-[4245.53px] w-[327.063px]"
        style={{ left: "calc(33.333% + 100.408px)" }}
      >
        <div className="flex-none rotate-[270deg]">
          <div
            className="[background-size:1.85047e+10%_1.14722e+10%,_auto] bg-[#2170e7] h-[327.077px] w-[309.005px]"
            style={{ backgroundImage: `url('${imgRectangle26}'), none` }}
          />
        </div>
      </div>
      <div
        className="absolute bg-[#181818] h-[309.005px] top-[4248.07px] w-[430.408px]"
        style={{ left: "calc(8.333% + 30px)" }}
      />
      <div
        className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#ffffff] text-[44px] text-left top-[4269.29px] w-[407.086px]"
        style={{ left: "calc(8.333% + 53.322px)" }}
      >
        <p className="block leading-[1.015]">“Big Dreams for lorem ipsum”</p>
      </div>
      <div
        className="absolute font-['Inter:Light',_sans-serif] font-light leading-[0] not-italic text-[#ffffff] text-[15px] text-left top-[4388.58px] w-[290.207px]"
        style={{ left: "calc(8.333% + 53.322px)" }}
      >
        <p className="block leading-[normal]">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit.Lorem ipsum
          dolor sit amet, consectetur adipiscing elit. Lorem ipsum dolor sit
          amet,
        </p>
      </div>
      <div
        className="absolute font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic text-[#ffffff] text-[16.067px] text-nowrap text-right top-[4509.61px] translate-x-[-100%]"
        style={{ left: "calc(8.333% + 167.489px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">{`See full review `}</p>
      </div>
      <div
        className="absolute h-[19.431px] top-[4511.89px] w-[30.276px]"
        style={{ left: "calc(16.667% + 55.064px)" }}
      >
        <div className="absolute bottom-[-3.984%] left-0 right-[-5.114%] top-[-3.984%]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 32 22"
          >
            <path
              d={svgPaths.ped78e80}
              id="Vector 7"
              stroke="var(--stroke-0, white)"
              strokeWidth="2.18959"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Button7() {
  return (
    <div
      className="absolute contents top-[4593.63px]"
      data-name="Button"
      style={{ left: "calc(41.667% - 1.993px)" }}
    >
      <div
        className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#000000] text-[21.778px] text-nowrap text-right top-[4619.26px] translate-x-[-100%]"
        style={{ left: "calc(41.667% + 195.566px)" }}
      >
        <p className="block leading-[normal] whitespace-pre">{`See  All Reviews`}</p>
      </div>
      <div
        className="absolute h-[77.8px] top-[4593.63px] w-[221.952px]"
        style={{ left: "calc(41.667% - 1.993px)" }}
      >
        <div className="absolute border-[#f8afa6] border-[1.361px] border-solid inset-0 pointer-events-none" />
      </div>
    </div>
  );
}

function Testimonials() {
  return (
    <div
      className="absolute contents top-[3640px]"
      data-name="Testimonials"
      style={{ left: "calc(8.333% + 30px)" }}
    >
      <Title />
      <Review1 />
      <Review2 />
      <Review3 />
      <Button7 />
    </div>
  );
}

export default function DigitalAgencyWebsite() {
  return (
    <div
      className="bg-[#ffffff] relative size-full"
      data-name="Digital Agency Website"
    >
      <HeroSection />
      <Section2 />
      <Section3 />
      <Section4 />
      <Testimonials />
    </div>
  );
}