"use client";
import React from 'react';
import { motion } from 'framer-motion';
import ArrowElement from './ArrowElement';
import PortfolioText from './PortfolioText';
import QuickLinks from './QuickLinks';

/**
 * PortfolioSection Component
 *
 * Portfolio showcase section matching the reference design layout.
 *
 * Layout Structure:
 * - Left side: Navigation arrows and hashtag text content
 * - Right side: Main orange rectangle with portfolio content
 * - Bottom: Quick links section with BLOG buttons
 *
 * Design Features:
 * - Asymmetric layout with 60-70% content on right
 * - Orange rounded containers (#FEB273)
 * - Black navigation arrows and hashtag text
 * - Responsive design for different screen sizes
 */
const PortfolioSection = () => {
  return (
    <section id="portfolio" className="relative w-full min-h-screen bg-white overflow-hidden py-12 z-10">
      {/* Main container with asymmetric layout */}
      <div className="relative w-full max-w-[1400px] mx-auto px-12">

        {/* Top section with left navigation and right main content */}
        <div className="relative flex flex-col lg:flex-row items-start justify-between mb-20 gap-8 lg:gap-0">

          {/* Left side - Navigation arrows and hashtag text */}
          <div className="w-full lg:w-[38%] flex flex-col items-start justify-start space-y-12 lg:space-y-16 pt-8 lg:pt-12 relative">

            {/* Large navigation arrow */}
            <div className="relative ml-4 lg:ml-12">
              <ArrowElement />
            </div>

            {/* Hashtag text content with decorative lines */}
            <div className="relative space-y-4 lg:space-y-6 ml-0 lg:ml-4 w-full">

              {/* First decorative line - above hashtags */}
              <motion.div
                className="absolute -top-4 left-0 right-8 h-[2px] bg-black opacity-30"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
              />

              <motion.div
                className="flex flex-wrap gap-x-8 lg:gap-x-12 gap-y-2 lg:gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#BRANDING</span>
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#LOGO</span>
              </motion.div>

              <motion.div
                className="flex flex-wrap gap-x-8 lg:gap-x-12 gap-y-2 lg:gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#SOCIAL_MEDIA</span>
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#POSTER</span>
              </motion.div>

              <motion.div
                className="flex flex-wrap gap-x-8 lg:gap-x-12 gap-y-2 lg:gap-y-3"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#Illustration</span>
                <span className="text-black font-['Gilroy:Medium'] text-lg lg:text-xl tracking-wide">#Packaging</span>
              </motion.div>

              {/* Second decorative line - below hashtags */}
              <motion.div
                className="absolute -bottom-4 left-0 right-12 h-[2px] bg-black opacity-25"
                initial={{ scaleX: 0 }}
                whileInView={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.9 }}
                viewport={{ once: true }}
              />

            </div>

          </div>

          {/* Right side - Main orange content rectangle */}
          <div className="w-full lg:w-[58%]">
            <motion.div
              className="relative bg-[#FEB273] rounded-[20px] lg:rounded-[24px] p-6 lg:p-10 min-h-[500px] lg:min-h-[750px] h-[70vh] lg:h-[85vh] overflow-hidden shadow-xl"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >

              {/* 2024 PORTFOLIO badge at top */}
              <motion.div
                className="absolute top-4 lg:top-8 left-4 lg:left-8 bg-[#FADCD9] rounded-[10px] lg:rounded-[12px] px-4 lg:px-6 py-2 lg:py-3"
                initial={{ opacity: 0, y: -20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
              >
                <span className="text-black font-['Gilroy:Bold'] text-base lg:text-lg tracking-wide">
                  2024 PORTFOLIO
                </span>
              </motion.div>

              {/* Main portfolio text content */}
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <PortfolioText />
              </div>

            </motion.div>
          </div>

        </div>


        {/* Bottom section - Quick Links */}
        <div className="w-full">
          <QuickLinks />
        </div>

      </div>
    </section>
  );
};

export default PortfolioSection;
