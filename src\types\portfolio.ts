export interface GridItem {
  id: string;
  type: 'slogan' | 'portrait' | 'work' | 'about' | 'contact' | 'social';
  backgroundColor: string;
  content: {
    title?: string;
    description?: string;
    image?: string;
    links?: string[];
    projects?: string[];
    socialLinks?: {
      platform: string;
      url: string;
    }[];
  };
}

export interface PortfolioGridProps {
  items?: GridItem[];
  className?: string;
  animate?: boolean;
}

export interface CircleDecorationProps {
  size?: number;
  opacity?: number;
  image: string;
  alt: string;
}
