import type { GridItem } from '../types/portfolio';

export const portfolioGridItems: GridItem[] = [
  {
    id: 'slogan',
    type: 'slogan',
    backgroundColor: '#fadcd9',
    content: {
      title: 'Artist Redefining Architecture',
      description: 'with AI-Driven Design'
    }
  },
  {
    id: 'portrait',
    type: 'portrait',
    backgroundColor: 'transparent',
    content: {
      image: '/img/layput_card-2.png'
    }
  },
  {
    id: 'work',
    type: 'work',
    backgroundColor: '#fadcd9',
    content: {
      title: 'Musea',
      description: 'Social Projects',
      image: '/img/layput_card-3.png',
      projects: ['Elara', 'Verve', 'Zephyr']
    }
  },
  {
    id: 'about',
    type: 'about',
    backgroundColor: '#fadcd9',
    content: {
      title: 'PER PIXEL',
      description: '<PERSON> is an innovative AI artist, renowned for blending cutting-edge technology with creative expression. Based in LA, she crafts unique digital art experiences accessible globally.'
    }
  },
  {
    id: 'contact',
    type: 'contact',
    backgroundColor: '#f8afa6',
    content: {
      title: 'Contact me',
      description: 'Have some questions?'
    }
  },
  {
    id: 'social',
    type: 'social',
    backgroundColor: '#fadcd9',
    content: {
      socialLinks: [
        { platform: 'INSTAGRAM', url: '#' },
        { platform: 'TWITTER', url: '#' },
        { platform: 'LINKEDIN', url: '#' }
      ]
    }
  }
];
