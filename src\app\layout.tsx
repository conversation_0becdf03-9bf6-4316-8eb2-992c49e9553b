import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON>, Playfair_Display } from "next/font/google";
import "./globals.css";
import React from 'react';
import Image from 'next/image';

import Navbar from "@/components/Navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

const playfairDisplay = Playfair_Display({
  variable: "--font-playfair-display",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "PerPixel Agency",
  description: "Generated by create next app",
};

// HeroShapes component moved from src/components/Hero Section/HeroShapes.tsx
const HeroShapes = () => (
  <>
    {/* Shape 1 - Circle with rotation and float ; Bubble (top left, bigger) */}
    <div className='absolute top-[-1rem] left-0 w-[10rem] h-[10rem] z-20'>
      <Image
        src='/img/circle.svg'
        alt='floating circle'
        width={160}
        height={160}
        className='object-contain scale-200 animate-spin-slow hover:scale-250 transition-transform'
        draggable={false}
      />
    </div>
    {/* Shape 2 - Cube with blur and scale (Cube 1 - top right) */}
    <div className='absolute top-0 right-0 w-[11rem] h-[11rem] animate-float-delayed z-20'>
      <Image
        src='/img/cube.svg'
        alt='floating cube'
        width={300}
        height={300}
        className='object-contain blur-[1px] scale-105 hover:blur-none transition-all transform rotate-[80deg]'
        draggable={false}
      />
    </div>
     {/* Shape 3 cube two scale and on left (Cube 2 - bottom left) */}
    <div className='absolute bottom-[-2rem] left-[-1rem] w-[11rem] h-[11rem] animate-float-delayed z-20'>
      <Image
        src='/img/cube.svg'
        alt='floating cube'
        width={300}
        height={300}
        className='object-contain blur-[1px] scale-200 hover:blur-none transition-all transform rotate-[100deg] transform-gpu'
        draggable={false}
      />
    </div>
    {/* Shape 4 - small Cube with blur, bigger size, and anticlockwise spin */}
    <div className='absolute top-[30rem] right-[33rem] w-[5rem] h-[5rem] animate-float-reverse z-20'>
      <Image
        src='/img/cube.svg'
        alt='floating cube'
        width={120}
        height={120}
        className='object-contain hover:blur-none transition-all transform-gpu animate-spin-slow'
        draggable={false}
      />
    </div>
    {/* Shape 5 - small Cube with blur, bigger size, and anticlockwise spin */}
    <div className='absolute bottom-[20rem] left-[34rem] w-[5rem] h-[5rem] animate-float-reverse z-20'>
      <Image
        src='/img/cube.svg'
        alt='floating cube'
        width={120}
        height={120}
        className='object-contain hover:blur-none transition-all transform-gpu animate-spin-reverse'
        draggable={false}
      />
    </div>
  </>
);

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} ${playfairDisplay.variable} antialiased`}
      >
        {/* HeroShapes moved from Hero Section */}
        <HeroShapes />

        <Navbar />
        {children}
      </body>
    </html>
  );
}
